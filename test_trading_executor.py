#!/usr/bin/env python3
"""
TradingExecutorAgent 快速測試腳本
測試 agent 的基本功能和策略部署能力
"""

import asyncio
import sys
import os

# 添加項目根目錄到 Python 路徑
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.agents.trading_executor_agent import TradingExecutorAgent, TradingRequest, TradingAction
from src.utils.strategy_types import DLMMStrategyType, StrategyFactory

async def test_agent_initialization():
    """測試 Agent 初始化"""
    print("🔧 測試 TradingExecutorAgent 初始化...")
    
    try:
        # 創建 Agent
        agent = TradingExecutorAgent({
            "name": "TestTradingExecutor",
            "model_provider": "ollama",
            "model_name": "qwen2.5:3b",
            "enable_reasoning": False,  # 簡化測試
            "enable_memory": False
        })
        
        # 初始化
        success = await agent.initialize()
        
        if success:
            print("✅ Agent 初始化成功")
            
            # 測試獲取支持的策略
            strategies = await agent.get_supported_strategies()
            print(f"📋 支持的策略數量: {len(strategies)}")
            
            for strategy in strategies:
                print(f"   - {strategy['name']} ({strategy['type']}): {strategy['risk_level']} 風險")
            
            # 清理
            await agent.cleanup()
            return True
        else:
            print("❌ Agent 初始化失敗")
            return False
            
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

async def test_strategy_creation():
    """測試策略創建"""
    print("\n🎯 測試策略創建...")
    
    try:
        # 測試四種策略類型
        strategy_tests = [
            {
                "type": "spot_balanced",
                "params": {
                    "token_amount": 1000.0,
                    "token_mint": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
                    "range_width": 20
                }
            },
            {
                "type": "curve_balanced", 
                "params": {
                    "token_amount": 2000.0,
                    "token_mint": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
                    "curve_steepness": 2.5,
                    "concentration_factor": 0.8
                }
            },
            {
                "type": "bid_ask_balanced",
                "params": {
                    "token_amount": 1500.0,
                    "token_mint": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
                    "bid_range": 15,
                    "ask_range": 15,
                    "bid_weight": 0.6,
                    "ask_weight": 0.4
                }
            },
            {
                "type": "spot_imbalanced",
                "params": {
                    "token_amount": 800.0,
                    "token_mint": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
                    "direction": "long",
                    "range_width": 25,
                    "concentration_bins": 5
                }
            }
        ]
        
        for test in strategy_tests:
            try:
                strategy_params = StrategyFactory.create_strategy_params(
                    strategy_type=test["type"],
                    **test["params"]
                )
                
                print(f"✅ {test['type']} 策略創建成功")
                print(f"   - 代幣數量: {strategy_params.token_amount}")
                print(f"   - 策略類型: {strategy_params.strategy_type.value}")
                
            except Exception as e:
                print(f"❌ {test['type']} 策略創建失敗: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 策略創建測試失敗: {e}")
        return False

async def test_trading_request_creation():
    """測試交易請求創建"""
    print("\n📝 測試交易請求創建...")
    
    try:
        # 測試不同類型的交易請求
        requests = [
            {
                "name": "簡單交換",
                "request": TradingRequest(
                    action=TradingAction.SWAP,
                    parameters={
                        "input_mint": "So11111111111111111111111111111111111111112",
                        "output_mint": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
                        "amount": "1000000000",
                        "user_public_key": "11111111111111111111111111111111"
                    }
                )
            },
            {
                "name": "LP 策略部署",
                "request": TradingRequest(
                    action=TradingAction.DEPLOY_LP,
                    parameters={
                        "pool_address": "8sLbNZoA1cfnvMJLPfp98ZLAnFSYCFApfJKMbiXNLwxj",
                        "strategy_type": "spot_balanced",
                        "token_amount": 1000.0,
                        "token_mint": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"
                    }
                )
            },
            {
                "name": "DCA 交換",
                "request": TradingRequest(
                    action=TradingAction.DCA_SWAP,
                    parameters={
                        "input_mint": "So11111111111111111111111111111111111111112",
                        "output_mint": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
                        "total_amount": 5000000000,
                        "intervals": 5,
                        "user_public_key": "11111111111111111111111111111111"
                    }
                )
            }
        ]
        
        for req_test in requests:
            request = req_test["request"]
            print(f"✅ {req_test['name']} 請求創建成功")
            print(f"   - 動作: {request.action.value}")
            print(f"   - 優先級: {request.priority}")
            print(f"   - 參數數量: {len(request.parameters)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 交易請求創建測試失敗: {e}")
        return False

async def test_mock_execution():
    """測試模擬執行 (不實際發送交易)"""
    print("\n🎮 測試模擬執行...")

    try:
        # 創建 Agent (不初始化工具以避免網絡請求)
        agent = TradingExecutorAgent({
            "name": "MockTestAgent",
            "model_provider": "ollama",
            "model_name": "qwen2.5:3b",
            "enable_reasoning": False,
            "enable_memory": False
        })

        # 測試執行統計
        stats = agent.get_execution_stats()
        print(f"✅ 執行統計獲取成功: {stats}")

        # 測試策略列表
        strategies = await agent.get_supported_strategies()
        print(f"✅ 策略列表獲取成功，共 {len(strategies)} 個策略")

        return True

    except Exception as e:
        print(f"❌ 模擬執行測試失敗: {e}")
        return False

async def main():
    """主測試函數"""
    print("🚀 開始 TradingExecutorAgent 測試")
    print("=" * 50)
    
    tests = [
        ("Agent 初始化", test_agent_initialization),
        ("策略創建", test_strategy_creation), 
        ("交易請求創建", test_trading_request_creation),
        ("模擬執行", test_mock_execution)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            print(f"\n🧪 執行測試: {test_name}")
            result = await test_func()
            results.append((test_name, result))
            
            if result:
                print(f"✅ {test_name} 測試通過")
            else:
                print(f"❌ {test_name} 測試失敗")
                
        except Exception as e:
            print(f"❌ {test_name} 測試異常: {e}")
            results.append((test_name, False))
    
    # 測試結果摘要
    print("\n" + "=" * 50)
    print("📊 測試結果摘要:")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"   {test_name}: {status}")
    
    print(f"\n總計: {passed}/{total} 個測試通過")
    
    if passed == total:
        print("🎉 所有測試都通過了！TradingExecutorAgent 基本功能正常。")
        return 0
    else:
        print("⚠️ 部分測試失敗，請檢查相關功能。")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
